# 📝 AI Notes Summarizer

A powerful web application that transforms lengthy documents and notes into concise, bullet-point summaries using state-of-the-art AI models.

## ✨ Features

- **PDF Processing**: Upload PDF files and extract text content automatically
- **Direct Text Input**: Paste text content directly for immediate summarization
- **AI-Powered Summarization**: Uses Hugging Face Transformers (BART, T5) for high-quality summaries
- **Bullet-Point Format**: Clean, readable bullet-point summaries
- **Multiple AI Models**: Choose from different pre-trained models
- **Customizable Length**: Adjust summary length (Short, Medium, Long)
- **Progress Tracking**: Real-time progress indicators during processing
- **Download Summaries**: Save generated summaries as text files
- **Statistics**: View compression ratios and word counts
- **Error Handling**: Comprehensive error handling and user feedback

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- pip (Python package installer)
- Internet connection (for downloading AI models)

### Installation

1. **Clone or download this repository**
   ```bash
   git clone <repository-url>
   cd ai-notes-summarizer
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   streamlit run app.py
   ```

4. **Open your browser**
   - The application will automatically open at `http://localhost:8501`
   - If it doesn't open automatically, navigate to the URL manually

## 📖 Usage Guide

### PDF Summarization

1. **Upload PDF**: Click on the "📄 PDF Upload" tab
2. **Select File**: Choose a PDF file (max 10MB)
3. **Process**: Click "📖 Extract & Summarize PDF"
4. **Review**: View the extracted text preview
5. **Get Summary**: The AI will generate a bullet-point summary
6. **Download**: Save the summary using the download button

### Text Summarization

1. **Input Text**: Click on the "📝 Text Input" tab
2. **Paste Content**: Enter or paste your text (minimum 100 characters)
3. **Summarize**: Click "🚀 Summarize Text"
4. **Review**: View the generated summary
5. **Download**: Save the summary as needed

### Settings

- **AI Model**: Choose from BART (recommended), T5, or DistilBART
- **Summary Length**: Select Short, Medium, or Long summaries
- **Statistics**: View word counts and compression ratios

## 🛠️ Technical Details

### Architecture

```
ai-notes-summarizer/
├── app.py                 # Main Streamlit application
├── modules/
│   ├── __init__.py
│   ├── pdf_processor.py   # PDF text extraction
│   ├── text_summarizer.py # AI summarization
│   └── utils.py          # Utility functions
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

### AI Models

- **BART (facebook/bart-large-cnn)**: Best quality, recommended for most use cases
- **T5 Small**: Faster processing, good for shorter texts
- **DistilBART**: Balanced performance and speed

### Dependencies

- **Streamlit**: Web application framework
- **Transformers**: Hugging Face AI models
- **PyTorch**: Deep learning framework
- **PyPDF2**: PDF text extraction
- **Additional utilities**: See `requirements.txt`

## 🔧 Configuration

### Model Selection

You can change the default model by modifying the `TextSummarizer` initialization in `app.py`:

```python
text_summarizer = TextSummarizer(model_name="your-preferred-model")
```

### Summary Length

Adjust default summary lengths in `modules/text_summarizer.py`:

```python
self.min_summary_length = 50  # Minimum words
self.max_summary_length = 300  # Maximum words
```

### File Size Limits

Modify PDF file size limits in `modules/pdf_processor.py`:

```python
self.max_file_size = 10 * 1024 * 1024  # 10MB
```

## 🚨 Troubleshooting

### Common Issues

1. **Model Loading Errors**
   - Ensure stable internet connection
   - Check available disk space (models can be 1-2GB)
   - Try switching to a smaller model (T5 Small or DistilBART)

2. **PDF Processing Issues**
   - Ensure PDF is not encrypted
   - Check if PDF contains readable text (not just images)
   - Try with a smaller PDF file

3. **Memory Errors**
   - Reduce text length
   - Close other applications
   - Try using CPU instead of GPU

4. **Slow Performance**
   - Use GPU if available
   - Choose smaller models for faster processing
   - Process shorter text chunks

### Error Messages

- **"Text is too short"**: Minimum 100 characters required
- **"No readable text found"**: PDF may contain only images
- **"Model loading error"**: Check internet connection
- **"Out of memory"**: Reduce text length or restart application

## 🎯 Best Practices

### For Best Results

1. **Text Quality**: Use well-formatted, coherent text
2. **Length**: Optimal text length is 500-5000 words
3. **Content**: Works best with structured content (articles, reports, notes)
4. **Model Choice**: Use BART for academic/formal content, T5 for general text

### Performance Tips

1. **GPU Usage**: Enable CUDA for faster processing
2. **Batch Processing**: Process multiple documents separately
3. **Model Caching**: Models are cached after first load
4. **Text Preprocessing**: Clean text improves summary quality

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

## 📞 Support

If you encounter any issues or have questions:

1. Check the troubleshooting section above
2. Review error messages for specific guidance
3. Ensure all dependencies are properly installed
4. Try with different models or settings

---

**Happy Summarizing! 📝✨**
