# Production-optimized Dockerfile for AI Notes Summarizer
FROM python:3.10-slim as base

# Build arguments
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION=1.0.0

# Labels for metadata
LABEL maintainer="AI Notes Summarizer Team" \
      org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="ai-notes-summarizer" \
      org.label-schema.description="AI-powered document summarization application" \
      org.label-schema.url="https://github.com/your-repo/ai-notes-summarizer" \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.version=$VERSION \
      org.label-schema.schema-version="1.0"

# Set environment variables for production
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    STREAMLIT_SERVER_HEADLESS=true \
    STREAMLIT_BROWSER_GATHER_USAGE_STATS=false \
    TRANSFORMERS_CACHE=/app/.cache/huggingface \
    TORCH_HOME=/app/.cache/torch \
    HF_HOME=/app/.cache/huggingface

# Install system dependencies and clean up in one layer
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN useradd --create-home --shell /bin/bash --uid 1000 app

# Stage 2: Dependencies
FROM base as dependencies

WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip cache purge

# Stage 3: Application
FROM dependencies as application

# Copy application code with proper ownership
COPY --chown=app:app . .

# Create necessary directories and set permissions
RUN mkdir -p /app/.cache /app/logs /app/uploads && \
    chown -R app:app /app && \
    chmod +x /app/*.sh 2>/dev/null || true

# Switch to non-root user
USER app

# Expose port
EXPOSE 8501

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8501/_stcore/health || exit 1

# Run the application
CMD ["streamlit", "run", "app.py", \
     "--server.port=8501", \
     "--server.address=0.0.0.0", \
     "--server.headless=true", \
     "--server.fileWatcherType=none", \
     "--browser.gatherUsageStats=false", \
     "--server.maxUploadSize=10"]
