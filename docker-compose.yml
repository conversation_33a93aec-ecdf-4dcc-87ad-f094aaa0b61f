version: '3.8'

services:
  ai-notes-summarizer:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai-notes-summarizer
    ports:
      - "8501:8501"
    environment:
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - STREAMLIT_SERVER_HEADLESS=true
      - STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
      - TRANSFORMERS_CACHE=/app/.cache/huggingface
      - TORCH_HOME=/app/.cache/torch
      - HF_HOME=/app/.cache/huggingface
    volumes:
      # Model cache persistence
      - model_cache:/app/.cache
      # Logs persistence
      - logs:/app/logs
      # Optional: Mount local uploads directory for development
      # - ./uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'

volumes:
  model_cache:
    driver: local
  logs:
    driver: local
