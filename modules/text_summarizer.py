"""
Text Summarization Module - Performance Optimized
Handles text summarization using Hugging Face Transformers with performance optimizations.
"""

from transformers import pipeline, AutoTokenizer, AutoModelForSeq2SeqLM
import torch
from typing import List, Optional, Dict, Tuple
import streamlit as st
import re
import hashlib
import asyncio
import concurrent.futures
from functools import lru_cache
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Performance-optimized model configurations
FAST_MODELS = {
    "distilbart": {
        "name": "sshleifer/distilbart-cnn-12-6",
        "description": "DistilBART - 40% faster, 97% quality",
        "speed": "fast",
        "quality": "high"
    },
    "t5-small": {
        "name": "t5-small",
        "description": "T5 Small - Very fast, good quality",
        "speed": "very_fast",
        "quality": "good"
    },
    "bart-large": {
        "name": "facebook/bart-large-cnn",
        "description": "BART Large - High quality, slower",
        "speed": "slow",
        "quality": "very_high"
    },
    "pegasus-xsum": {
        "name": "google/pegasus-xsum",
        "description": "Pegasus - Optimized for short summaries",
        "speed": "medium",
        "quality": "high"
    }
}

class OptimizedTextSummarizer:
    """Performance-optimized text summarization using pre-trained models"""

    def __init__(self, model_name: str = "distilbart", use_cache: bool = True):
        """
        Initialize the optimized text summarizer

        Args:
            model_name: Model key from FAST_MODELS or full model name
            use_cache: Enable result caching for faster repeated summaries
        """
        # Resolve model name
        if model_name in FAST_MODELS:
            self.model_config = FAST_MODELS[model_name]
            self.model_name = self.model_config["name"]
        else:
            self.model_name = model_name
            self.model_config = {"name": model_name, "speed": "unknown", "quality": "unknown"}

        # Core components
        self.summarizer = None
        self.tokenizer = None
        self.model = None

        # Performance settings
        self.use_cache = use_cache
        self.max_chunk_length = 512  # Reduced for faster processing
        self.min_summary_length = 30  # Reduced for speed
        self.max_summary_length = 150  # Reduced for speed
        self.batch_size = 4  # Process multiple chunks in parallel

        # Caching
        self._summary_cache = {}
        self._model_loaded = False

        # Performance tracking
        self.performance_stats = {
            "model_load_time": 0,
            "total_summaries": 0,
            "cache_hits": 0,
            "average_processing_time": 0
        }
    
    def _get_cache_key(self, text: str) -> str:
        """Generate cache key for text"""
        return hashlib.md5(f"{self.model_name}:{text}".encode()).hexdigest()

    def _get_cached_summary(self, text: str) -> Optional[str]:
        """Retrieve cached summary if available"""
        if not self.use_cache:
            return None

        cache_key = self._get_cache_key(text)
        if cache_key in self._summary_cache:
            self.performance_stats["cache_hits"] += 1
            logger.info(f"Cache hit for text hash: {cache_key[:8]}...")
            return self._summary_cache[cache_key]
        return None

    def _cache_summary(self, text: str, summary: str):
        """Cache summary result"""
        if not self.use_cache:
            return

        cache_key = self._get_cache_key(text)
        self._summary_cache[cache_key] = summary

        # Limit cache size to prevent memory issues
        if len(self._summary_cache) > 100:
            # Remove oldest entries (simple FIFO)
            oldest_key = next(iter(self._summary_cache))
            del self._summary_cache[oldest_key]

    @st.cache_resource
    def load_model(_self):
        """
        Load the optimized summarization model and tokenizer with performance enhancements
        """
        start_time = time.time()

        try:
            # Determine optimal device and settings
            device = 0 if torch.cuda.is_available() else -1
            use_fp16 = torch.cuda.is_available()

            # Show device and model info
            model_info = _self.model_config.get("description", _self.model_name)
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name()
                st.info(f"🚀 Loading {model_info} on GPU: {gpu_name}")
            else:
                st.info(f"💻 Loading {model_info} on CPU (consider GPU for better performance)")

            # Load tokenizer first (faster)
            _self.tokenizer = AutoTokenizer.from_pretrained(
                _self.model_name,
                use_fast=True,  # Use fast tokenizer when available
                padding_side="left"  # Optimize for batch processing
            )

            # Load model with optimizations
            model_kwargs = {
                "torch_dtype": torch.float16 if use_fp16 else torch.float32,
                "low_cpu_mem_usage": True,  # Reduce memory usage during loading
            }

            # Load model directly for more control
            _self.model = AutoModelForSeq2SeqLM.from_pretrained(
                _self.model_name,
                **model_kwargs
            )

            # Create optimized pipeline
            _self.summarizer = pipeline(
                "summarization",
                model=_self.model,
                tokenizer=_self.tokenizer,
                device=device,
                batch_size=_self.batch_size,  # Enable batch processing
                **model_kwargs
            )

            # Enable model optimizations
            if hasattr(_self.model, 'half') and use_fp16:
                _self.model.half()  # Use half precision for speed

            if hasattr(_self.model, 'eval'):
                _self.model.eval()  # Set to evaluation mode

            # Track loading time
            load_time = time.time() - start_time
            _self.performance_stats["model_load_time"] = load_time
            _self._model_loaded = True

            st.success(f"✅ {model_info} loaded in {load_time:.2f}s")
            return True

        except Exception as e:
            return _self._handle_model_loading_error(e, start_time)

    def _handle_model_loading_error(self, error: Exception, start_time: float) -> bool:
        """Handle model loading errors with fallback strategies"""
        load_time = time.time() - start_time

        if isinstance(error, OSError):
            if "Connection error" in str(error) or "timeout" in str(error).lower():
                st.error("❌ Network error: Could not download the model. Please check your internet connection.")
            else:
                st.error(f"❌ Model loading error: {str(error)}")
            return False

        elif isinstance(error, RuntimeError) and "CUDA" in str(error):
            st.warning("⚠️ GPU memory error. Falling back to CPU...")
            return self._load_model_cpu_fallback()

        else:
            st.error(f"❌ Unexpected error loading model: {str(error)}")
            logger.error(f"Model loading failed after {load_time:.2f}s: {error}")
            return False

    def _load_model_cpu_fallback(self) -> bool:
        """Fallback to CPU-only model loading"""
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name, use_fast=True)
            self.model = AutoModelForSeq2SeqLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float32,
                low_cpu_mem_usage=True
            )

            self.summarizer = pipeline(
                "summarization",
                model=self.model,
                tokenizer=self.tokenizer,
                device=-1,  # Force CPU
                batch_size=2  # Reduce batch size for CPU
            )

            self._model_loaded = True
            st.success("✅ Model loaded successfully on CPU")
            return True

        except Exception as cpu_error:
            st.error(f"❌ Failed to load model on CPU: {str(cpu_error)}")
            return False
    
    def chunk_text_optimized(self, text: str) -> List[str]:
        """
        Optimized text chunking with smart sentence boundary detection

        Args:
            text: Input text to chunk

        Returns:
            List[str]: List of optimally sized text chunks
        """
        # Quick sentence-based chunking for better performance
        sentences = re.split(r'(?<=[.!?])\s+', text)
        chunks = []
        current_chunk = ""
        target_chunk_size = 1000  # Characters, not tokens for speed

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # Check if adding this sentence would exceed chunk size
            if len(current_chunk) + len(sentence) > target_chunk_size and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = sentence
            else:
                current_chunk += " " + sentence if current_chunk else sentence

        # Add the last chunk
        if current_chunk:
            chunks.append(current_chunk.strip())

        # Filter out very short chunks
        chunks = [chunk for chunk in chunks if len(chunk.split()) >= 20]

        return chunks

    def chunk_text(self, text: str) -> List[str]:
        """Backward compatibility wrapper"""
        return self.chunk_text_optimized(text)

    def summarize_batch(self, chunks: List[str]) -> List[Optional[str]]:
        """
        Summarize multiple chunks in batch for better performance

        Args:
            chunks: List of text chunks to summarize

        Returns:
            List[Optional[str]]: List of summaries (None for failed chunks)
        """
        if not chunks:
            return []

        summaries = []
        batch_size = min(self.batch_size, len(chunks))

        # Process chunks in batches
        for i in range(0, len(chunks), batch_size):
            batch = chunks[i:i + batch_size]

            try:
                # Prepare batch parameters
                batch_params = []
                for chunk in batch:
                    chunk_length = len(chunk.split())
                    max_length = min(self.max_summary_length, max(self.min_summary_length, chunk_length // 3))
                    min_length = min(self.min_summary_length, max_length // 2)

                    batch_params.append({
                        "max_length": max_length,
                        "min_length": min_length,
                        "do_sample": False,
                        "truncation": True,
                        "clean_up_tokenization_spaces": True
                    })

                # Process batch
                if len(batch) == 1:
                    # Single item processing
                    result = self.summarizer(batch[0], **batch_params[0])
                    batch_summaries = [result[0]['summary_text']]
                else:
                    # Batch processing
                    results = self.summarizer(batch, **batch_params[0])  # Use first params for batch
                    batch_summaries = [result['summary_text'] for result in results]

                summaries.extend(batch_summaries)

            except Exception as e:
                logger.warning(f"Batch processing failed: {e}")
                # Fallback to individual processing
                for chunk in batch:
                    try:
                        individual_summary = self.summarize_chunk_fallback(chunk)
                        summaries.append(individual_summary)
                    except Exception as individual_e:
                        logger.warning(f"Individual chunk processing failed: {individual_e}")
                        summaries.append(None)

        return summaries

    def summarize_chunk_fallback(self, chunk: str) -> Optional[str]:
        """Fallback method for individual chunk processing"""
        try:
            chunk_length = len(chunk.split())
            max_length = min(self.max_summary_length, max(self.min_summary_length, chunk_length // 3))
            min_length = min(self.min_summary_length, max_length // 2)

            result = self.summarizer(
                chunk,
                max_length=max_length,
                min_length=min_length,
                do_sample=False,
                truncation=True
            )

            return result[0]['summary_text']
        except Exception as e:
            logger.warning(f"Chunk summarization failed: {e}")
            return None
    
    def summarize_chunk(self, chunk: str) -> Optional[str]:
        """
        Backward compatibility wrapper for single chunk summarization
        """
        return self.summarize_chunk_fallback(chunk)
    
    def format_as_bullets(self, summary_text: str) -> str:
        """
        Format summary text as bullet points
        
        Args:
            summary_text: Raw summary text
            
        Returns:
            str: Formatted bullet points
        """
        # Split by sentences and create bullet points
        sentences = re.split(r'[.!?]+', summary_text)
        bullets = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and len(sentence) > 10:  # Filter out very short fragments
                bullets.append(f"• {sentence}")
        
        return '\n'.join(bullets)
    
    def summarize_text(self, text: str) -> Optional[str]:
        """
        Optimized text summarization pipeline with caching and batch processing

        Args:
            text: Input text to summarize

        Returns:
            str: Formatted summary or None if summarization fails
        """
        start_time = time.time()

        # Input validation
        if not text or len(text.strip()) < 100:
            st.error("❌ Text is too short to summarize effectively (minimum 100 characters required)")
            return None

        # Check cache first
        cached_result = self._get_cached_summary(text)
        if cached_result:
            st.success("⚡ Retrieved from cache (instant result!)")
            return cached_result

        # Check text length limits
        word_count = len(text.split())
        if word_count > 15000:
            st.warning(f"⚠️ Very large text detected ({word_count:,} words). Consider splitting into smaller sections.")
        elif word_count > 5000:
            st.info(f"📄 Large text detected ({word_count:,} words). Using optimized processing...")

        try:
            # Load model if not already loaded
            if not self._model_loaded:
                with st.spinner("🤖 Loading optimized AI model..."):
                    if not self.load_model():
                        return None

            # Optimized text chunking
            chunks = self.chunk_text_optimized(text)

            if len(chunks) == 0:
                st.error("❌ Could not process the text into chunks")
                return None

            # Show processing info
            model_info = self.model_config.get("description", "AI model")
            st.info(f"🚀 Processing {len(chunks)} chunk(s) with {model_info}")

            # Batch processing for better performance
            progress_bar = st.progress(0)

            with st.spinner("⚡ Processing chunks in parallel..."):
                summaries = self.summarize_batch(chunks)

            # Filter successful summaries
            successful_summaries = [s for s in summaries if s is not None]
            failed_chunks = len(summaries) - len(successful_summaries)

            progress_bar.progress(1.0)

            # Check results
            if not successful_summaries:
                st.error("❌ Could not generate any summaries from the text")
                return None

            if failed_chunks > 0:
                st.warning(f"⚠️ {failed_chunks} out of {len(chunks)} chunks failed to process")

            # Combine summaries intelligently
            combined_summary = self._combine_summaries(successful_summaries, len(chunks))

            # Format as bullet points
            formatted_summary = self.format_as_bullets(combined_summary)

            if not formatted_summary.strip():
                st.error("❌ Generated summary is empty")
                return None

            # Cache the result
            self._cache_summary(text, formatted_summary)

            # Update performance stats
            processing_time = time.time() - start_time
            self.performance_stats["total_summaries"] += 1
            self.performance_stats["average_processing_time"] = (
                (self.performance_stats["average_processing_time"] * (self.performance_stats["total_summaries"] - 1) + processing_time)
                / self.performance_stats["total_summaries"]
            )

            # Show performance info
            st.success(f"✅ Summary generated in {processing_time:.2f}s")

            return formatted_summary

        except MemoryError:
            st.error("❌ Out of memory. Try with shorter text or restart the application.")
            return None
        except Exception as e:
            st.error(f"❌ Unexpected error during summarization: {str(e)}")
            logger.error(f"Summarization failed: {e}")
            return None

    def _combine_summaries(self, summaries: List[str], original_chunks: int) -> str:
        """
        Intelligently combine multiple summaries

        Args:
            summaries: List of chunk summaries
            original_chunks: Number of original chunks

        Returns:
            str: Combined summary
        """
        if len(summaries) == 1:
            return summaries[0]

        # Join summaries
        combined = ' '.join(summaries)

        # If combined summary is too long and we had many chunks, create a meta-summary
        combined_words = len(combined.split())
        if original_chunks > 3 and combined_words > 300:
            try:
                with st.spinner("🔄 Creating final consolidated summary..."):
                    meta_summary = self.summarize_chunk_fallback(combined)
                    if meta_summary:
                        return meta_summary
            except Exception as e:
                logger.warning(f"Meta-summary failed: {e}")

        return combined

    def get_performance_stats(self) -> Dict:
        """Get performance statistics"""
        return self.performance_stats.copy()

    def clear_cache(self):
        """Clear the summary cache"""
        self._summary_cache.clear()
        st.success("🗑️ Cache cleared successfully")
