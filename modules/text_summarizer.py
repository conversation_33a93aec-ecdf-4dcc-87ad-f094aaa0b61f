"""
Text Summarization Module - Performance Optimized
Handles text summarization using Hugging Face Transformers with performance optimizations.
"""

from transformers import pipeline, AutoTokenizer
import torch
from typing import List, Optional, Dict
import streamlit as st
import re
import time
import hashlib
from functools import lru_cache

class TextSummarizer:
    """Performance-optimized text summarization using faster pre-trained models"""

    # Fast model configurations optimized for speed vs quality
    FAST_MODELS = {
        "distilbart": {
            "name": "sshleifer/distilbart-cnn-12-6",
            "description": "DistilBART CNN - 40% faster, 97% quality retention",
            "speed": "⚡ Fast",
            "quality": "🎯 High",
            "recommended": True
        },
        "distilbart-xsum": {
            "name": "sshleifer/distilbart-xsum-12-6",
            "description": "DistilBART XSum - Optimized for concise summaries",
            "speed": "⚡ Fast",
            "quality": "🎯 High",
            "recommended": False
        },
        "t5-small": {
            "name": "t5-small",
            "description": "T5 Small - Very fast, good quality",
            "speed": "🚀 Very Fast",
            "quality": "✅ Good",
            "recommended": True
        },
        "flan-t5-small": {
            "name": "google/flan-t5-small",
            "description": "FLAN-T5 Small - Instruction-tuned, very fast",
            "speed": "🚀 Very Fast",
            "quality": "✅ Good",
            "recommended": True
        },
        "bart-large": {
            "name": "facebook/bart-large-cnn",
            "description": "BART Large - High quality, slower (original)",
            "speed": "🐌 Slow",
            "quality": "🏆 Excellent",
            "recommended": False
        }
    }

    def __init__(self, model_name: str = "distilbart", enable_caching: bool = True):
        """
        Initialize the performance-optimized text summarizer

        Args:
            model_name: Model key from FAST_MODELS or custom model name
            enable_caching: Enable result caching for faster repeated summaries
        """
        # Resolve model configuration
        if model_name in self.FAST_MODELS:
            self.model_config = self.FAST_MODELS[model_name]
            self.model_name = self.model_config["name"]
            self.model_key = model_name
        else:
            self.model_name = model_name
            self.model_key = model_name
            self.model_config = {
                "name": model_name,
                "description": f"Custom model: {model_name}",
                "speed": "❓ Unknown",
                "quality": "❓ Unknown",
                "recommended": False
            }

        # Core components
        self.summarizer = None
        self.tokenizer = None

        # Performance optimizations
        self.enable_caching = enable_caching
        self.result_cache = {}

        # Optimized parameters for faster processing
        self.max_chunk_length = 512  # Reduced from 1024 for faster processing
        self.min_summary_length = 30  # Reduced minimum
        self.max_summary_length = 150  # Reduced maximum for faster generation

        # Performance tracking
        self.performance_stats = {
            "total_summaries": 0,
            "cache_hits": 0,
            "avg_processing_time": 0.0,
            "total_processing_time": 0.0
        }
    
    @st.cache_resource
    def load_model(_self):
        """
        Load the optimized summarization model with performance enhancements
        """
        start_time = time.time()

        try:
            # Determine optimal device configuration
            device = _self._get_optimal_device()
            torch_dtype = torch.float16 if torch.cuda.is_available() else torch.float32

            # Display model information
            model_info = _self.model_config["description"]
            speed_info = _self.model_config["speed"]
            quality_info = _self.model_config["quality"]

            st.info(f"🤖 Loading {model_info}")
            st.info(f"📊 Performance: {speed_info} | {quality_info}")

            # Show device information
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name()
                st.info(f"🚀 Using GPU acceleration: {gpu_name}")
            else:
                st.info("💻 Using optimized CPU processing")

            # Load tokenizer first (faster loading)
            _self.tokenizer = AutoTokenizer.from_pretrained(
                _self.model_name,
                use_fast=True  # Use fast tokenizer when available
            )

            # Load the optimized summarization pipeline
            _self.summarizer = pipeline(
                "summarization",
                model=_self.model_name,
                tokenizer=_self.tokenizer,
                device=device,
                torch_dtype=torch_dtype,
                # Performance optimizations
                model_kwargs={
                    "low_cpu_mem_usage": True,
                    "use_cache": True
                }
            )

            # Optimize inference settings
            if hasattr(_self.summarizer.model, 'config'):
                # Enable optimizations for faster inference
                _self.summarizer.model.config.use_cache = True

            load_time = time.time() - start_time
            st.success(f"✅ Model loaded in {load_time:.1f}s | Ready for fast summarization!")

            return True

        except Exception as e:
            return _self._handle_model_loading_error(e, start_time)

    def _get_optimal_device(self):
        """Determine the optimal device for model inference"""
        if torch.cuda.is_available():
            # Check available GPU memory
            try:
                gpu_memory = torch.cuda.get_device_properties(0).total_memory
                # Use GPU if we have sufficient memory (>2GB for small models)
                if gpu_memory > 2 * 1024**3:
                    return 0
                else:
                    st.warning("⚠️ Limited GPU memory detected, using CPU for better stability")
                    return -1
            except:
                return -1
        return -1

    def _handle_model_loading_error(self, error, start_time):
        """Handle model loading errors with intelligent fallbacks"""
        error_msg = str(error)

        if "Connection error" in error_msg or "timeout" in error_msg.lower():
            st.error("❌ Network error: Could not download the model. Please check your internet connection.")
        elif "CUDA" in error_msg or "GPU" in error_msg:
            st.warning("⚠️ GPU error detected, falling back to CPU...")
            return self._fallback_to_cpu()
        elif "memory" in error_msg.lower() or "OOM" in error_msg:
            st.warning("⚠️ Memory error, trying with optimized settings...")
            return self._fallback_optimized()
        else:
            st.error(f"❌ Model loading error: {error_msg}")
            # Try fallback for unknown errors
            return self._fallback_to_cpu()

        return False

    def _fallback_to_cpu(self):
        """Fallback to CPU-only inference with optimizations"""
        try:
            st.info("🔄 Attempting CPU fallback...")

            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name, use_fast=True)
            self.summarizer = pipeline(
                "summarization",
                model=self.model_name,
                tokenizer=self.tokenizer,
                device=-1,  # Force CPU
                torch_dtype=torch.float32,
                model_kwargs={"low_cpu_mem_usage": True}
            )

            st.success("✅ Model loaded successfully on CPU with optimizations")
            return True

        except Exception as e:
            st.error(f"❌ CPU fallback failed: {str(e)}")
            return False

    def _fallback_optimized(self):
        """Fallback with memory-optimized settings"""
        try:
            st.info("🔄 Attempting memory-optimized loading...")

            # Reduce chunk size for memory efficiency
            self.max_chunk_length = 256
            self.max_summary_length = 100

            return self._fallback_to_cpu()

        except Exception as e:
            st.error(f"❌ Optimized fallback failed: {str(e)}")
            return False
    
    @lru_cache(maxsize=64)
    def _get_text_hash(self, text: str) -> str:
        """Generate a hash for text caching"""
        return hashlib.md5(text.encode()).hexdigest()[:16]

    def get_cached_summary(self, text: str) -> Optional[str]:
        """Check if summary exists in cache"""
        if not self.enable_caching:
            return None

        cache_key = f"{self._get_text_hash(text)}_{self.model_key}_{self.max_summary_length}"

        if cache_key in self.result_cache:
            self.performance_stats["cache_hits"] += 1
            return self.result_cache[cache_key]

        return None

    def cache_summary(self, text: str, summary: str):
        """Cache a summary result"""
        if not self.enable_caching:
            return

        cache_key = f"{self._get_text_hash(text)}_{self.model_key}_{self.max_summary_length}"
        self.result_cache[cache_key] = summary

        # Limit cache size to prevent memory issues
        if len(self.result_cache) > 100:
            # Remove oldest entries (simple FIFO)
            oldest_keys = list(self.result_cache.keys())[:20]
            for key in oldest_keys:
                del self.result_cache[key]

    def chunk_text(self, text: str) -> List[str]:
        """
        Optimized text chunking for faster processing

        Args:
            text: Input text to chunk

        Returns:
            List[str]: List of optimally sized text chunks
        """
        # Check cache for chunking results
        if self.enable_caching:
            chunk_cache_key = f"chunks_{self._get_text_hash(text)}_{self.max_chunk_length}"
            if chunk_cache_key in self.result_cache:
                return self.result_cache[chunk_cache_key]

        chunks = []

        if not self.tokenizer:
            # Fast sentence-based chunking fallback
            chunks = self._chunk_by_sentences_fast(text)
        else:
            # Optimized token-based chunking
            chunks = self._chunk_by_tokens_optimized(text)

        # Cache the chunking result
        if self.enable_caching:
            self.result_cache[chunk_cache_key] = chunks

        return chunks

    def _chunk_by_sentences_fast(self, text: str) -> List[str]:
        """Fast sentence-based chunking for fallback"""
        sentences = re.split(r'(?<=[.!?])\s+', text)
        chunks = []
        current_chunk = ""
        # Approximate character to token ratio for faster processing
        target_length = self.max_chunk_length * 3

        for sentence in sentences:
            if len(current_chunk) + len(sentence) < target_length:
                current_chunk += sentence + " "
            else:
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + " "

        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        # Filter out very short chunks
        return [chunk for chunk in chunks if len(chunk.strip()) > 100]

    def _chunk_by_tokens_optimized(self, text: str) -> List[str]:
        """Optimized token-based chunking with smart sentence boundaries"""
        # Split by sentences first for better chunk boundaries
        sentences = re.split(r'(?<=[.!?])\s+', text)
        chunks = []
        current_tokens = []

        for sentence in sentences:
            # Quick token count estimation (faster than actual tokenization)
            estimated_tokens = len(sentence.split()) * 1.3  # Rough estimation

            if len(current_tokens) + estimated_tokens <= self.max_chunk_length:
                current_tokens.extend(sentence.split())
            else:
                if current_tokens:
                    chunk_text = " ".join(current_tokens)
                    chunks.append(chunk_text)
                current_tokens = sentence.split()

        if current_tokens:
            chunk_text = " ".join(current_tokens)
            chunks.append(chunk_text)

        return chunks
    
    def summarize_chunks_batch(self, chunks: List[str]) -> List[Optional[str]]:
        """
        Summarize multiple chunks with optimized batch processing

        Args:
            chunks: List of text chunks to summarize

        Returns:
            List[Optional[str]]: List of summaries (None for failed chunks)
        """
        if not chunks:
            return []

        summaries = []

        # Process chunks with optimized parameters
        for i, chunk in enumerate(chunks):
            try:
                summary = self._summarize_chunk_optimized(chunk)
                summaries.append(summary)

                # Update progress more efficiently
                if i % max(1, len(chunks) // 10) == 0:  # Update every 10%
                    progress = (i + 1) / len(chunks)
                    st.session_state.get('progress_bar', lambda x: None)(progress)

            except Exception as e:
                st.warning(f"⚠️ Failed to summarize chunk {i+1}: {str(e)}")
                summaries.append(None)

        return summaries

    def _summarize_chunk_optimized(self, chunk: str) -> Optional[str]:
        """Optimized single chunk summarization with faster inference"""
        try:
            # Dynamic length adjustment for faster processing
            chunk_length = len(chunk.split())

            # Optimized length calculation for speed
            if chunk_length < 100:
                max_length = min(50, chunk_length // 2)
                min_length = min(20, max_length // 2)
            else:
                max_length = min(self.max_summary_length, chunk_length // 4)  # More aggressive compression
                min_length = min(self.min_summary_length, max_length // 3)

            # Optimized inference parameters for speed
            summary = self.summarizer(
                chunk,
                max_length=max_length,
                min_length=min_length,
                do_sample=False,  # Deterministic for speed
                truncation=True,
                early_stopping=True,  # Stop early when possible
                num_beams=1,  # Reduced from default for speed (greedy decoding)
                no_repeat_ngram_size=2,  # Prevent repetition
                length_penalty=1.0  # Neutral length penalty
            )

            return summary[0]['summary_text'] if summary else None

        except Exception as e:
            return None

    def summarize_chunk(self, chunk: str) -> Optional[str]:
        """
        Summarize a single text chunk (legacy method for compatibility)

        Args:
            chunk: Text chunk to summarize

        Returns:
            str: Summary of the chunk or None if summarization fails
        """
        return self._summarize_chunk_optimized(chunk)
    
    def format_as_bullets(self, summary_text: str) -> str:
        """
        Format summary text as bullet points
        
        Args:
            summary_text: Raw summary text
            
        Returns:
            str: Formatted bullet points
        """
        # Split by sentences and create bullet points
        sentences = re.split(r'[.!?]+', summary_text)
        bullets = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and len(sentence) > 10:  # Filter out very short fragments
                bullets.append(f"• {sentence}")
        
        return '\n'.join(bullets)
    
    def summarize_text(self, text: str) -> Optional[str]:
        """
        Performance-optimized text summarization pipeline with caching and speed improvements

        Args:
            text: Input text to summarize

        Returns:
            str: Formatted summary or None if summarization fails
        """
        start_time = time.time()

        # Input validation
        if not text or len(text.strip()) < 100:
            st.error("❌ Text is too short to summarize effectively (minimum 100 characters required)")
            return None

        # Check cache first for instant results
        cached_summary = self.get_cached_summary(text)
        if cached_summary:
            st.success("⚡ Retrieved from cache - Instant result!")
            return cached_summary

        # Performance-aware text length checking
        word_count = len(text.split())
        if word_count > 5000:
            st.warning(f"⚠️ Large text detected ({word_count:,} words). Using optimized fast processing...")
        elif word_count > 10000:
            st.error("❌ Text too large. Please use text under 10,000 words for optimal performance.")
            return None

        try:
            # Load model if not already loaded
            if not self.summarizer:
                with st.spinner("🤖 Loading optimized AI model..."):
                    if not self.load_model():
                        return None

            # Optimized text chunking
            with st.spinner("📄 Analyzing text structure..."):
                chunks = self.chunk_text(text)

            if len(chunks) == 0:
                st.error("❌ Could not process the text into chunks")
                return None

            # Show processing information with model details
            model_speed = self.model_config["speed"]
            st.info(f"⚡ Processing {len(chunks)} chunk(s) with {model_speed} model")

            # Set up progress tracking
            progress_bar = st.progress(0)
            st.session_state.progress_bar = progress_bar.progress

            # Batch processing for better performance
            with st.spinner("🚀 Generating summaries with optimized inference..."):
                summaries = self.summarize_chunks_batch(chunks)

            # Filter successful summaries
            successful_summaries = [s for s in summaries if s is not None]
            failed_chunks = len(summaries) - len(successful_summaries)

            if not successful_summaries:
                st.error("❌ Could not generate any summaries from the text")
                return None

            if failed_chunks > 0:
                st.warning(f"⚠️ {failed_chunks} out of {len(chunks)} chunks failed to process")

            # Intelligently combine summaries
            combined_summary = self._combine_summaries_optimized(successful_summaries)

            # Format as bullet points
            formatted_summary = self.format_as_bullets(combined_summary)

            if not formatted_summary.strip():
                st.error("❌ Generated summary is empty")
                return None

            # Cache the result for future use
            self.cache_summary(text, formatted_summary)

            # Update performance statistics
            processing_time = time.time() - start_time
            self._update_performance_stats(processing_time, word_count)

            # Show performance information
            speed_info = self._get_speed_info(processing_time, word_count)
            st.success(f"✅ Summary generated in {processing_time:.1f}s {speed_info}")

            return formatted_summary

        except MemoryError:
            st.error("❌ Out of memory. Try with shorter text or restart the application.")
            return None
        except Exception as e:
            st.error(f"❌ Unexpected error during summarization: {str(e)}")
            return None

    def _combine_summaries_optimized(self, summaries: List[str]) -> str:
        """Intelligently combine multiple summaries with length optimization"""
        if len(summaries) == 1:
            return summaries[0]

        combined = ' '.join(summaries)

        # If combined summary is reasonable length, return as-is
        if len(combined.split()) <= self.max_summary_length * 1.2:
            return combined

        # Create meta-summary only if really necessary
        try:
            with st.spinner("🔄 Creating optimized final summary..."):
                meta_summary = self._summarize_chunk_optimized(combined)
                return meta_summary if meta_summary else combined
        except Exception:
            return combined

    def _update_performance_stats(self, processing_time: float, word_count: int):
        """Update performance tracking statistics"""
        self.performance_stats["total_summaries"] += 1
        self.performance_stats["total_processing_time"] += processing_time
        self.performance_stats["avg_processing_time"] = (
            self.performance_stats["total_processing_time"] /
            self.performance_stats["total_summaries"]
        )

    def _get_speed_info(self, processing_time: float, word_count: int) -> str:
        """Generate speed performance information"""
        words_per_second = word_count / processing_time if processing_time > 0 else 0

        if words_per_second > 200:
            return f"({words_per_second:.0f} words/sec - 🚀 Excellent!)"
        elif words_per_second > 100:
            return f"({words_per_second:.0f} words/sec - ⚡ Very Good!)"
        elif words_per_second > 50:
            return f"({words_per_second:.0f} words/sec - ✅ Good)"
        else:
            return f"({words_per_second:.0f} words/sec)"

    def get_performance_stats(self) -> Dict:
        """Get current performance statistics for display"""
        total_summaries = self.performance_stats["total_summaries"]
        cache_hit_rate = (
            (self.performance_stats["cache_hits"] / max(total_summaries, 1)) * 100
        )

        return {
            "model": self.model_config["description"],
            "total_summaries": total_summaries,
            "cache_hits": self.performance_stats["cache_hits"],
            "cache_hit_rate": f"{cache_hit_rate:.1f}%",
            "avg_processing_time": f"{self.performance_stats['avg_processing_time']:.1f}s",
            "cache_size": len(self.result_cache),
            "speed_rating": self.model_config["speed"],
            "quality_rating": self.model_config["quality"]
        }

    def clear_cache(self):
        """Clear the result cache to free memory"""
        cache_size = len(self.result_cache)
        self.result_cache.clear()
        st.success(f"🧹 Cleared {cache_size} cached results")

    @classmethod
    def get_available_models(cls) -> Dict:
        """Get information about all available fast models"""
        return cls.FAST_MODELS
